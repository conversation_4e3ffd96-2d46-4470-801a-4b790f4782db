{"version": 3, "file": "match-path-async.js", "sourceRoot": "", "sources": ["../src/match-path-async.ts"], "names": [], "mappings": ";;;AAAA,2BAA6B;AAC7B,oCAAsC;AACtC,8CAAgD;AAChD,yCAA2C;AAmB3C;;GAEG;AACH,SAAgB,oBAAoB,CAClC,eAAuB,EACvB,KAAuC,EACvC,UAA+B,EAC/B,WAA2B;IAD3B,2BAAA,EAAA,cAAwB,MAAM,CAAC;IAC/B,4BAAA,EAAA,kBAA2B;IAE3B,IAAM,aAAa,GAAG,YAAY,CAAC,yBAAyB,CAC1D,eAAe,EACf,KAAK,EACL,WAAW,CACZ,CAAC;IAEF,OAAO,UACL,eAAuB,EACvB,QAA8C,EAC9C,UAAkD,EAClD,UAA6C,EAC7C,QAAgC;QAEhC,OAAA,2BAA2B,CACzB,aAAa,EACb,eAAe,EACf,QAAQ,EACR,UAAU,EACV,UAAU,EACV,QAAQ,EACR,UAAU,CACX;IARD,CAQC,CAAC;AACN,CAAC;AA5BD,oDA4BC;AAED;;GAEG;AACH,SAAgB,2BAA2B,CACzC,oBAA8D,EAC9D,eAAuB,EACvB,QAAqE,EACrE,UAAmE,EACnE,UAAmE,EACnE,QAAgC,EAChC,UAA+B;IAJ/B,yBAAA,EAAA,WAAqC,UAAU,CAAC,qBAAqB;IACrE,2BAAA,EAAA,aAAyC,UAAU,CAAC,eAAe;IACnE,2BAAA,EAAA,aAAoC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC;IAEnE,2BAAA,EAAA,cAAwB,MAAM,CAAC;IAE/B,IAAM,QAAQ,GAAG,OAAO,CAAC,aAAa,CACpC,UAAU,EACV,oBAAoB,EACpB,eAAe,CAChB,CAAC;IAEF,IAAI,CAAC,QAAQ,EAAE;QACb,OAAO,QAAQ,EAAE,CAAC;KACnB;IAED,qBAAqB,CACnB,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,CAAC,EACD,UAAU,CACX,CAAC;AACJ,CAAC;AA3BD,kEA2BC;AAED,SAAS,oCAAoC,CAC3C,WAAmC,EACnC,UAAoB,EACpB,eAAuB,EACvB,eAA2C,EAC3C,YAAsD,EACtD,KAAiB;IAAjB,sBAAA,EAAA,SAAiB;IAEjB,IAAI,KAAK,IAAI,UAAU,CAAC,MAAM,EAAE;QAC9B,OAAO,YAAY,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;KAC3C;IAED,IAAM,OAAO,GAAG;QACd,OAAA,oCAAoC,CAClC,WAAW,EACX,UAAU,EACV,eAAe,EACf,eAAe,EACf,YAAY,EACZ,KAAK,GAAG,CAAC,CACV;IAPD,CAOC,CAAC;IAEJ,IAAM,gBAAgB,GAAG,WAAW,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;IACxD,IAAI,OAAO,gBAAgB,KAAK,QAAQ,EAAE;QACxC,2DAA2D;QAC3D,OAAO,OAAO,EAAE,CAAC;KAClB;IAED,IAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAC9B,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,EAC7B,gBAAgB,CACjB,CAAC;IACF,eAAe,CAAC,cAAc,EAAE,UAAC,GAAW,EAAE,MAAgB;QAC5D,IAAI,GAAG,EAAE;YACP,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC;SAC1B;QACD,IAAI,MAAM,EAAE;YACV,OAAO,YAAY,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;SAChD;QACD,OAAO,OAAO,EAAE,CAAC;IACnB,CAAC,CAAC,CAAC;AACL,CAAC;AAED,6CAA6C;AAC7C,SAAS,qBAAqB,CAC5B,QAAwC,EACxC,QAAkC,EAClC,UAAsC,EACtC,YAAoC,EACpC,KAAiB,EACjB,UAA+B;IAD/B,sBAAA,EAAA,SAAiB;IACjB,2BAAA,EAAA,cAAwB,MAAM,CAAC;IAE/B,IAAM,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAChC,IACE,OAAO,CAAC,IAAI,KAAK,MAAM;QACvB,OAAO,CAAC,IAAI,KAAK,WAAW;QAC5B,OAAO,CAAC,IAAI,KAAK,OAAO,EACxB;QACA,UAAU,CAAC,OAAO,CAAC,IAAI,EAAE,UAAC,GAAU,EAAE,MAAe;YACnD,IAAI,GAAG,EAAE;gBACP,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC;aAC1B;YACD,IAAI,MAAM,EAAE;gBACV,OAAO,YAAY,CAAC,SAAS,EAAE,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC;aAClE;YACD,IAAI,KAAK,KAAK,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;gBACjC,OAAO,YAAY,EAAE,CAAC;aACvB;YACD,8BAA8B;YAC9B,OAAO,qBAAqB,CAC1B,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,KAAK,GAAG,CAAC,EACT,UAAU,CACX,CAAC;QACJ,CAAC,CAAC,CAAC;KACJ;SAAM,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;QACrC,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,UAAC,GAAG,EAAE,WAAW;YACtC,IAAI,GAAG,EAAE;gBACP,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC;aAC1B;YACD,IAAI,WAAW,EAAE;gBACf,OAAO,oCAAoC,CACzC,WAAW,EACX,UAAU,EACV,OAAO,CAAC,IAAI,EACZ,UAAU,EACV,UAAC,YAAoB,EAAE,mBAA4B;oBACjD,IAAI,YAAY,EAAE;wBAChB,OAAO,YAAY,CAAC,YAAY,CAAC,CAAC;qBACnC;oBACD,IAAI,mBAAmB,EAAE;wBACvB,OAAO,YAAY,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;qBACrD;oBAED,4EAA4E;oBAC5E,OAAO,qBAAqB,CAC1B,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,KAAK,GAAG,CAAC,EACT,UAAU,CACX,CAAC;gBACJ,CAAC,CACF,CAAC;aACH;YAED,wFAAwF;YACxF,8FAA8F;YAC9F,4EAA4E;YAC5E,EAAE;YACF,0CAA0C;YAC1C,qEAAqE;YACrE,EAAE;YACF,8BAA8B;YAC9B,OAAO,qBAAqB,CAC1B,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,YAAY,EACZ,KAAK,GAAG,CAAC,EACT,UAAU,CACX,CAAC;QACJ,CAAC,CAAC,CAAC;KACJ;SAAM;QACL,OAAO,CAAC,uBAAuB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;KAC/C;AACH,CAAC"}